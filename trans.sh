#!/bin/bash

lenguajeOrigen="es"
lenguajeDestino="en"

# Modo debug si se pasa el parámetro --debug
DEBUG_MODE=false
if [ "$1" = "--debug" ]; then
    DEBUG_MODE=true
    echo "=== MODO DEBUG ACTIVADO ==="
fi

# Mapear a Tesseract
case "$lenguajeOrigen" in
  es) langTesseract="spa" ;;
  en) langTesseract="eng" ;;
  fr) langTesseract="fra" ;;
  de) langTesseract="deu" ;;
  it) langTesseract="ita" ;;
  pt) langTesseract="por" ;;
  *) langTesseract="eng" ;;
esac

# Cerrar ventana si está abierta
eww close translate 2>/dev/null

# Ejecutar OCR y traducción paso a paso para debug
echo "Iniciando captura de pantalla..."
hyprshot --mode region --output-folder /tmp --filename captura_temp.png --silent

if [ ! -f /tmp/captura_temp.png ]; then
    echo "Error: No se encontró el archivo de captura"
    TRADUCCION="Error: No se pudo capturar la pantalla"
else
    echo "Archivo de captura creado correctamente"
    echo "Ejecutando OCR..."
    TEXTO_OCR=$(tesseract /tmp/captura_temp.png stdout -l "$langTesseract" 2>/dev/null)
    echo "OCR completado"

    if [ -z "$TEXTO_OCR" ]; then
        TRADUCCION="Error: No se pudo extraer texto de la imagen"
    else
        #echo "Texto extraído: $TEXTO_OCR"

        # Limpiar el texto OCR de caracteres problemáticos
        TEXTO_LIMPIO=$(echo "$TEXTO_OCR" | tr -d '\r' | sed 's/[[:space:]]\+/ /g' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')

        if [ -z "$TEXTO_LIMPIO" ]; then
            echo "Error: Texto limpio está vacío"
            TRADUCCION="Error: El texto extraído está vacío después de limpieza"
        else
            #echo "Texto limpio: '$TEXTO_LIMPIO'"
            echo "Traduciendo..."
            # Usar timeout para evitar que se cuelgue el comando trans
            TRADUCCION=$(timeout 15s bash -c "printf '%s\n' '$TEXTO_LIMPIO' | trans '$lenguajeOrigen:$lenguajeDestino' -brief 2>/dev/null")

            # Verificar si el comando falló por timeout o error
            if [ $? -eq 124 ]; then
                echo "Error: Timeout en la traducción"
                TRADUCCION="Error: Timeout en la traducción"
            elif [ -z "$TRADUCCION" ]; then
                echo "Error: Traducción vacía"
                TRADUCCION="Error: No se pudo traducir el texto extraído: '$TEXTO_LIMPIO'"
            else
                echo "Traducción completada: $TRADUCCION"
            fi
        fi
    fi

    # Limpiar archivo temporal
    rm -f /tmp/captura_temp.png
fi

# Actualizar la variable en EWW con la traducción y mostrar ventana
if [ "$DEBUG_MODE" = true ]; then
    echo "=== RESULTADO FINAL ==="
    echo "Traducción: $TRADUCCION"
    echo "=== FIN DEBUG ==="
else
    eww update traduccion="$TRADUCCION"
    eww open translate

    # Esperar y cerrar
    sleep 30
    eww close translate
fi
