/* ~/.config/eww/style.css */
.popup {
  background: linear-gradient(135deg, rgba(30, 30, 46, 0.95), rgba(49, 50, 68, 0.9));
  border-radius: 20px;
  padding: 25px;
  color: white;
  font-size: 16px;
  min-width: 400px;
  border: 2px solid rgba(137, 180, 250, 0.4);
}

.translate-box {
  /* Efecto glass moderno para Hyprland */
  background: linear-gradient(135deg,
    rgba(30, 30, 46, 0.85) 0%,
    rgba(49, 50, 68, 0.8) 50%,
    rgba(30, 30, 46, 0.85) 100%);
  border-radius: 24px;
  padding: 30px;
  color: white;
  font-size: 14px;
  min-width: 450px;
  border: 2px solid rgba(137, 180, 250, 0.5);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset,
    0 2px 0 rgba(255, 255, 255, 0.2) inset;
}

.translate-title {
  color: #89b4fa;
  font-family: "Inter", "SF Pro Display", "Segoe UI", sans-serif;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
  opacity: 0.95;
}

.translate-text {
  color: #f8f8f2;
  font-family: "Inter", "SF Pro Text", "Segoe UI", sans-serif;
  font-size: 16px;
  font-weight: 400;
  background: linear-gradient(135deg,
    rgba(49, 50, 68, 0.7) 0%,
    rgba(68, 71, 90, 0.6) 100%);
  padding: 25px;
  border-radius: 18px;
  border-left: 5px solid #89b4fa;
  margin-top: 15px;
  border: 1px solid rgba(137, 180, 250, 0.3);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
  min-height: 60px;
}
