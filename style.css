/* ~/.config/eww/style.css */
.popup {
  background-color: rgba(30, 30, 46, 0.2);

  border-radius: 12px;
  padding: 20px;
  color: white;
  font-size: 16px;
  min-width: 400px;
}

.translate-box {
  /* Efecto glass transparente para Hyprland */
  background: rgba(30, 30, 46, 0.7);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 25px;
  color: white;
  font-size: 14px;
  min-width: 300px;
  max-width: 600px;
  border: 15px solid rgba(137, 180, 250, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  /* Centrar el contenido */
  margin: 0 auto;
}

.translate-title {
  color: #89b4fa;
  font-family: "JetBrains Mono", monospace;
  font-size: 54px;
  font-weight: bold;
  margin-bottom: 15px;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.translate-text {
  color: #cdd6f4;
  font-family: "JetBrains Mono", monospace;
  font-size: 14px;
  line-height: 1.6;
  background: rgba(49, 50, 68, 0.5);
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #89b4fa;
  text-align: center;
  margin-top: 10px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(137, 180, 250, 0.2);
}
