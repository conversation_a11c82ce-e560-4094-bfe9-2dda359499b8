(defvar traduccion "")

(defwindow translate
  :monitor 0
  :geometry (geometry :x "50%" :y "8%" :width "600px" :height "auto" :anchor "top center")
  :stacking "fg"
  :exclusive false
  :focusable false
  :wm-ignore false
  (box
    :class "translate-box"
    :orientation "vertical"
    :spacing 20
    :halign "center"
    :valign "start"
    (label
      :class "translate-title"
      :text "🌐 Traducción"
      :halign "center")
    (label
      :class "translate-text"
      :text traduccion
      :wrap true
      :halign "center"
      :valign "center")
  )
)
