(defvar traduccion "")

(defwindow translate
  :geometry (geometry :x "50%" :y "10%" :width "500px" :height "300px" :anchor "top center")
  :stacking "fg"
  :exclusive false
  :focusable false
  :wm-ignore false
  (box
    :class "translate-box"
    :orientation "vertical"
    :spacing 15
    :halign "center"
    :valign "start"
    (label
      :class "translate-title"
      :text "🌐 Traducción"
      :halign "center")
    (label
      :class "translate-text"
      :text traduccion
      :wrap true
      :halign "center"
      :valign "center")
  )
)
